<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <UContainer class="py-8">
      <!-- Header -->
      <div class="flex items-center justify-between mb-8">
        <div class="flex items-center space-x-4">
          <UButton
            @click="$router.push('/')"
            variant="ghost"
            icon="i-lucide-arrow-left"
            size="lg"
          >
            Back to Home
          </UButton>
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
              Notation Conversion Game
            </h1>
            <p class="text-gray-600 dark:text-gray-300">
              Convert between different mathematical notations
            </p>
          </div>
        </div>

        <!-- Progress -->
        <div class="text-right">
          <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {{ gameStore.completionPercentage }}%
          </div>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            {{ gameStore.state.totalCompleted }} / 37 completed
          </div>
        </div>
      </div>

      <!-- Progress Bar -->
      <div class="mb-8">
        <div class="flex justify-between text-sm text-gray-600 dark:text-gray-300 mb-2">
          <span>Progress</span>
          <span>{{ gameStore.state.totalCompleted }} / 37</span>
        </div>
        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
          <div
            class="bg-blue-600 h-3 rounded-full transition-all duration-500"
            :style="{ width: `${gameStore.completionPercentage}%` }"
          ></div>
        </div>
      </div>

      <!-- Game Grid -->
      <GameGrid @item-selected="handleItemSelected" />

      <!-- Problem Modal -->
      <ProblemModal
        v-if="gameStore.state.showModal && gameStore.state.currentProblem"
        :problem="gameStore.state.currentProblem"
        :current-answer="gameStore.state.currentAnswer"
        @answer-updated="gameStore.updateAnswer"
        @answer-submitted="handleAnswerSubmitted"
        @modal-closed="gameStore.closeModal"
      />

      <!-- Completion Modal -->
      <UModal
        v-model="showCompletionModal"
        role="dialog"
        :aria-labelledby="completionModalTitleId"
        aria-describedby="completion-modal-description"
      >
        <UCard>
          <template #header>
            <div class="flex items-center space-x-3">
              <Icon name="i-lucide-trophy" class="w-8 h-8 text-yellow-500" />
              <h3 :id="completionModalTitleId" class="text-xl font-bold">Congratulations!</h3>
            </div>
          </template>

          <div id="completion-modal-description" class="sr-only">
            Game completion dialog. You have successfully completed all problems in the Notation Conversion game.
          </div>

          <div class="space-y-4">
            <p class="text-gray-600 dark:text-gray-300">
              You've completed all 37 notation conversion problems!
            </p>

            <div class="grid grid-cols-2 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">{{ gameStore.gameStats.totalAttempts }}</div>
                <div class="text-sm text-gray-500">Total Attempts</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-green-600">{{ Math.round(gameStore.gameStats.completionRate * 100) }}%</div>
                <div class="text-sm text-gray-500">Success Rate</div>
              </div>
            </div>

            <div class="flex space-x-3">
              <UButton @click="restartGame" class="flex-1">
                Play Again
              </UButton>
              <UButton @click="$router.push('/')" variant="outline" class="flex-1">
                Back to Home
              </UButton>
            </div>
          </div>
        </UCard>
      </UModal>
    </UContainer>
  </div>
</template>

<script setup lang="ts">
import { validateAnswer } from '~/utils/mathValidation'

// SEO Meta
useSeoMeta({
  title: 'Notation Conversion Game - Math Game Center',
  description: 'Practice converting between algebraic statements, interval notation, and set-builder notation.',
})

// Store
const gameStore = useGameStore()

// State
const currentItemId = ref<number | null>(null)
const showCompletionModal = ref(false)

// Generate unique ID for accessibility
const completionModalTitleId = `completion-modal-title-${Math.random().toString(36).substring(2, 11)}`

// Initialize game on mount
onMounted(() => {
  gameStore.initializeGame('notation-conversion')
})

// Watch for game completion
watch(() => gameStore.isGameComplete, (isComplete) => {
  if (isComplete) {
    showCompletionModal.value = true
  }
})

// Handlers
const handleItemSelected = (itemId: number) => {
  currentItemId.value = itemId
  gameStore.selectItem(itemId)
}

const handleAnswerSubmitted = () => {
  if (!gameStore.state.currentProblem || !currentItemId.value) return

  const validation = validateAnswer(
    gameStore.state.currentAnswer,
    gameStore.state.currentProblem
  )

  gameStore.submitAnswer(validation.isCorrect, currentItemId.value)

  // Show feedback
  if (validation.isCorrect) {
    // Success feedback is handled by the store
  } else {
    // Error feedback is shown in the modal
  }
}

const restartGame = () => {
  showCompletionModal.value = false
  gameStore.resetGame()
  gameStore.initializeGame('notation-conversion')
}
</script>